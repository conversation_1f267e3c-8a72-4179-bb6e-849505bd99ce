#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库修复脚本
用于修复外键约束问题
"""

import os
import sys
import yaml
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_config():
    """加载配置文件"""
    config_path = os.path.join(os.path.dirname(__file__), "config.yaml")
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def get_db_engine(config):
    """获取数据库引擎"""
    pg_config = config.get("postgres", {})
    pg_host = pg_config.get("host", "localhost")
    pg_port = pg_config.get("port", 5432)
    pg_user = pg_config.get("user", "postgres")
    pg_password = pg_config.get("password", "password")
    pg_db = pg_config.get("database", "knowledge_db")
    
    db_url = f"postgresql://{pg_user}:{pg_password}@{pg_host}:{pg_port}/{pg_db}"
    return create_engine(db_url)

def drop_tables(engine):
    """删除所有相关表"""
    print("正在删除现有表...")
    
    with engine.connect() as conn:
        # 按依赖关系顺序删除表
        tables_to_drop = [
            'knowledge_nodes',
            'knowledge_files', 
            'knowledge_databases'
        ]
        
        for table in tables_to_drop:
            try:
                conn.execute(text(f"DROP TABLE IF EXISTS {table} CASCADE"))
                print(f"已删除表: {table}")
            except Exception as e:
                print(f"删除表 {table} 时出错: {e}")
        
        conn.commit()

def create_tables(engine):
    """重新创建表"""
    print("正在重新创建表...")
    
    # 导入模型
    from packages.models.kb_models import Base
    
    # 创建所有表
    Base.metadata.create_all(engine)
    print("表创建完成!")

def main():
    """主函数"""
    try:
        # 加载配置
        config = load_config()
        
        # 获取数据库引擎
        engine = get_db_engine(config)
        
        # 测试连接
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("数据库连接成功!")
        
        # 删除现有表
        drop_tables(engine)
        
        # 重新创建表
        create_tables(engine)
        
        print("数据库修复完成!")
        
    except Exception as e:
        print(f"修复过程中出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
