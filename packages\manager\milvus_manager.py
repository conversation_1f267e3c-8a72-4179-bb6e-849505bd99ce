import os
import subprocess
import time
import signal
import psutil
import threading
from pathlib import Path
from ..utils import logger


class MilvusManager:
    """Milvus服务器管理器"""

    def __init__(self, data_dir="./milvus_lite", port=19530, host="127.0.0.1"):
        self.data_dir = Path(data_dir).resolve()
        self.port = port
        self.host = host
        self.process = None
        self.is_running = False

        # 确保数据目录存在
        self.data_dir.mkdir(parents=True, exist_ok=True)

    def _check_milvus_installed(self):
        """检查pymilvus是否已安装"""
        try:
            import pymilvus
            # 检查版本是否支持 Milvus Lite
            version = pymilvus.__version__
            major, minor, patch = map(int, version.split('.'))
            if major > 2 or (major == 2 and minor >= 4):
                return True
            else:
                logger.warning(f"pymilvus版本 {version} 不支持 Milvus Lite，需要 2.4.0 或更高版本")
                return False
        except ImportError as e:
            logger.warning(f"pymilvus依赖缺失: {e}")
            return False

    def _install_milvus(self):
        """安装或升级pymilvus"""
        logger.info("正在安装/升级pymilvus依赖...")
        try:
            # 安装或升级pymilvus到最新版本
            subprocess.run(['pip', 'install', '--upgrade', 'pymilvus'], check=True)
            logger.info("pymilvus安装/升级成功")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"pymilvus依赖安装失败: {e}")
            return False

    def _is_port_in_use(self, port):
        """检查端口是否被占用"""
        for conn in psutil.net_connections():
            if conn.laddr.port == port:
                return True
        return False

    def _get_process_using_port(self, port):
        """获取占用指定端口的进程ID"""
        for conn in psutil.net_connections():
            if conn.laddr.port == port and conn.pid:
                return conn.pid
        return None

    def _kill_process_using_port(self, port):
        """杀死占用指定端口的进程"""
        pid = self._get_process_using_port(port)
        if pid:
            try:
                process = psutil.Process(pid)
                process_name = process.name()
                logger.info(f"正在关闭占用端口 {port} 的进程: {process_name} (PID: {pid})")

                # 先尝试优雅关闭
                process.terminate()

                # 等待进程结束
                try:
                    process.wait(timeout=5)
                    logger.info(f"进程 {process_name} (PID: {pid}) 已优雅关闭")
                    return True
                except psutil.TimeoutExpired:
                    # 强制杀死进程
                    process.kill()
                    process.wait(timeout=5)
                    logger.info(f"进程 {process_name} (PID: {pid}) 已强制关闭")
                    return True

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
                logger.warning(f"无法关闭进程 {pid}: {e}")
                return False
        return False

    def _test_milvus_connection(self):
        """测试Milvus Lite连接"""
        try:
            from pymilvus import MilvusClient
            # 使用本地文件路径连接milvus-lite
            client = MilvusClient(uri=str(self.data_dir / "milvus.db"))
            # 测试基本操作
            collections = client.list_collections()
            logger.info(f"Milvus Lite连接成功，数据目录: {self.data_dir}")
            logger.info(f"现有集合数量: {len(collections)}")
            client.close()
            return True
        except Exception as e:
            logger.error(f"Milvus Lite连接失败: {e}")
            return False

    def start(self):
        """启动Milvus Lite（实际上是测试连接）"""
        if self.is_running:
            logger.info("Milvus Lite已在运行")
            return True

        # 检查是否已安装pymilvus
        if not self._check_milvus_installed():
            logger.info("未检测到合适的pymilvus版本，正在安装/升级...")
            if not self._install_milvus():
                logger.error("无法安装/升级pymilvus依赖，请手动安装: pip install --upgrade pymilvus")
                return False

        try:
            logger.info(f"正在初始化Milvus Lite，数据目录: {self.data_dir}")

            # 测试Milvus Lite连接
            if self._test_milvus_connection():
                self.is_running = True
                logger.info("Milvus Lite初始化成功")

                # 自动加载所有现有的集合
                logger.info("正在加载现有的集合...")
                self.load_all_collections()

                return True
            else:
                logger.error("Milvus Lite初始化失败")
                return False

        except Exception as e:
            logger.error(f"初始化Milvus Lite失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def _start_log_monitor(self):
        """启动日志监控线程"""
        def monitor_logs():
            if self.process:
                for line in iter(self.process.stdout.readline, ''):
                    if line.strip():
                        logger.debug(f"Milvus: {line.strip()}")
                    if not self.is_running:
                        break

        log_thread = threading.Thread(target=monitor_logs, daemon=True)
        log_thread.start()

    def stop(self, force_kill_port=True):
        """停止Milvus Lite

        Args:
            force_kill_port (bool): 是否强制关闭占用端口的进程（Milvus Lite不需要）
        """
        self.is_running = False
        logger.info("Milvus Lite已停止（无需额外操作）")

    def restart(self):
        """重启milvus服务器"""
        logger.info("正在重启milvus服务器...")
        self.stop()
        time.sleep(2)
        return self.start()

    def load_all_collections(self):
        """加载所有现有的集合到内存中（Milvus Lite自动管理）"""
        try:
            from pymilvus import MilvusClient
            # 使用本地文件路径连接milvus-lite
            client = MilvusClient(uri=str(self.data_dir / "milvus.db"))
            collections = client.list_collections()

            # Milvus Lite 自动管理集合加载，无需手动加载
            logger.info(f"发现 {len(collections)} 个集合: {collections}")

            client.close()
            return len(collections)

        except Exception as e:
            logger.error(f"Failed to list collections: {e}")
            return 0

    def get_status(self):
        """获取Milvus Lite状态"""
        if not self.is_running:
            return {"status": "stopped", "data_dir": str(self.data_dir)}

        try:
            from pymilvus import MilvusClient
            # 使用本地文件路径连接milvus-lite
            client = MilvusClient(uri=str(self.data_dir / "milvus.db"))
            collections = client.list_collections()
            client.close()
            return {
                "status": "running",
                "data_dir": str(self.data_dir),
                "collections_count": len(collections),
                "collections": collections
            }
        except Exception as e:
            return {
                "status": "error",
                "data_dir": str(self.data_dir),
                "error": str(e)
            }


# 全局milvus管理器实例
milvus_manager = None

def get_milvus_manager(data_dir="./milvus_lite", port=19530, host="127.0.0.1"):
    """获取milvus管理器实例"""
    global milvus_manager
    if milvus_manager is None:
        milvus_manager = MilvusManager(data_dir, port, host)
    return milvus_manager

def start_milvus_server(data_dir="./milvus_lite", port=19530, host="127.0.0.1"):
    """启动milvus服务器的便捷函数"""
    manager = get_milvus_manager(data_dir, port, host)
    return manager.start()

def stop_milvus_server(force_kill_port=True):
    """停止milvus服务器的便捷函数"""
    global milvus_manager
    if milvus_manager:
        milvus_manager.stop(force_kill_port=force_kill_port)

def force_kill_milvus_port(port=19530):
    """强制关闭占用Milvus端口的进程"""
    manager = MilvusManager(port=port)
    if manager._is_port_in_use(port):
        logger.info(f"正在强制关闭占用端口 {port} 的进程...")
        if manager._kill_process_using_port(port):
            logger.info(f"已成功关闭占用端口 {port} 的进程")
            return True
        else:
            logger.warning(f"无法关闭占用端口 {port} 的进程")
            return False
    else:
        logger.info(f"端口 {port} 未被占用")
        return True
