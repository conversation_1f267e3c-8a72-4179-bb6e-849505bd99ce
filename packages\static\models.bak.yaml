MODEL_NAMES:
  # openai:
  #   name: OpenAI
  #   url: https://platform.openai.com/docs/models
  #   default: gpt-3.5-turbo
  #   env:
  #     - OPENAI_API_KEY
  #   models:
  #     - gpt-4
  #     - gpt-4o
  #     - gpt-4o-mini
  #     - gpt-3.5-turbo
  deepseek:
    name: DeepSeek
    url: https://platform.deepseek.com/api-docs/zh-cn/pricing
    default: deepseek-chat
    base_url: https://api.deepseek.com/v1
    env:
      - DEEPSEEK_API_KEY
    models:
      - deepseek-chat
      - deepseek-reasoner
  # siliconflow:
  #   name: SiliconFlow
  #   url: https://cloud.siliconflow.cn/models
  #   default: Qwen/Qwen2.5-7B-Instruct
  #   base_url: https://api.siliconflow.cn/v1
  #   env:
  #     - SILICONFLOW_API_KEY
  #   models:
  #     - Pro/deepseek-ai/DeepSeek-R1
  #     - Pro/deepseek-ai/DeepSeek-V3
  #     - deepseek-ai/DeepSeek-R1
  #     - deepseek-ai/DeepSeek-V3
  #     - deepseek-ai/DeepSeek-R1-Di<PERSON>ill-Qwen-7B
  #     - Qwen/Qwen2.5-72B-Instruct
  #     - Qwen/Qwen2.5-7B-Instruct
  #     - Qwen/QwQ-32B
  


EMBED_MODEL_INFO:
  local/BAAI/bge-m3:
    name: BAAI/bge-m3
    dimension: 1024
    local_path: models\embedding_model\bge-m3 #也可以在这里配置

  zhipu/zhipu-embedding-2:
    name: embedding-2
    dimension: 1024

  zhipu/zhipu-embedding-3:
    name: embedding-3
    dimension: 2048


RERANKER_LIST:

  local/BAAI/bge-m3:
    name: BAAI/bge-m3
    local_path: models\embedding_model\bge-m3 #也可以在这里配置

  # siliconflow/BAAI/bge-reranker-v2-m3:
  #   name: BAAI/bge-reranker-v2-m3
